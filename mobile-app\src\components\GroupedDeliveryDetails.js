import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    FlatList,
    Alert,
    Linking,
    Platform
} from 'react-native';
import { Icon, <PERSON><PERSON>, Card } from 'react-native-elements';
import { colors } from '../common/theme';
import { fonts } from '../common/font';
import i18n from 'i18n-js';
import MapView, { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, PROVIDER_GOOGLE } from 'react-native-maps';


export default function GroupedDeliveryDetails({ 
    deliveryData,
    onUpdateDelivery,
    onCompleteOrder,
    currentLocation 
}) {
    const { t } = i18n;
    const isRTL = i18n.locale.indexOf('he') === 0 || i18n.locale.indexOf('ar') === 0;
    
    const [showChecklist, setShowChecklist] = useState(false);
    const [orderStatuses, setOrderStatuses] = useState({});
    const [currentOrderIndex, setCurrentOrderIndex] = useState(0);

    const orders = deliveryData?.orders || [];
    const folio = deliveryData?.folio || '';

    useEffect(() => {
        // Inicializar estados de las órdenes
        const initialStatuses = {};
        orders.forEach((order, index) => {
            initialStatuses[index] = order.status || 'pending';
        });
        setOrderStatuses(initialStatuses);
    }, [orders]);

    const getOrderStatusColor = (status) => {
        switch (status) {
            case 'completed': return colors.GREEN;
            case 'in_progress': return colors.BLUE;
            case 'partial': return colors.ORANGE;
            case 'pending': return colors.GREY;
            default: return colors.GREY;
        }
    };

    const getOrderStatusIcon = (status) => {
        switch (status) {
            case 'completed': return 'check-circle';
            case 'in_progress': return 'access-time';
            case 'partial': return 'warning';
            case 'pending': return 'radio-button-unchecked';
            default: return 'radio-button-unchecked';
        }
    };

    const getOrderStatusText = (status) => {
        switch (status) {
            case 'completed': return 'Completada';
            case 'in_progress': return 'En Progreso';
            case 'partial': return 'Parcial';
            case 'pending': return 'Pendiente';
            default: return 'Pendiente';
        }
    };

    const openNavigation = (address) => {
        const destination = encodeURIComponent(address);
        const url = Platform.select({
            ios: `maps://app?daddr=${destination}`,
            android: `google.navigation:q=${destination}`
        });

        Linking.canOpenURL(url).then(supported => {
            if (supported) {
                Linking.openURL(url);
            } else {
                Alert.alert('Error', 'No se puede abrir la aplicación de mapas');
            }
        });
    };

    const startOrder = (orderIndex) => {
        const order = orders[orderIndex];
        setCurrentOrderIndex(orderIndex);

        // Actualizar estado a "en progreso"
        setOrderStatuses(prev => ({
            ...prev,
            [orderIndex]: 'in_progress'
        }));

        // Mostrar mensaje de confirmación directamente
        Alert.alert(
            'Entrega Iniciada',
            `Se ha iniciado la entrega para ${order.customerName}. Dirígete al punto de entrega.`,
            [{ text: 'OK' }]
        );
    };

    const completeOrder = (orderIndex) => {
        const order = orders[orderIndex];
        setSelectedOrder(order);
        setCurrentOrderIndex(orderIndex);
        setShowChecklist(true);
    };

    const handleCompleteDelivery = (deliveryData) => {
        const orderIndex = currentOrderIndex;
        const isPartial = deliveryData.verification.partialDelivery;
        
        // Actualizar estado de la orden
        setOrderStatuses(prev => ({
            ...prev,
            [orderIndex]: isPartial ? 'partial' : 'completed'
        }));

        // Notificar al componente padre
        onCompleteOrder(orderIndex, deliveryData);
        
        setShowChecklist(false);
        setSelectedOrder(null);

        Alert.alert(
            'Entrega Registrada', 
            isPartial ? 'Entrega parcial completada' : 'Entrega completada exitosamente'
        );
    };

    const getCompletedOrdersCount = () => {
        return Object.values(orderStatuses).filter(status => 
            status === 'completed' || status === 'partial'
        ).length;
    };

    const getTotalProductsCount = () => {
        return orders.reduce((total, order) => {
            return total + (order.products?.length || 0);
        }, 0);
    };

    const renderOrderCard = ({ item, index }) => {
        const status = orderStatuses[index] || 'pending';
        const statusColor = getOrderStatusColor(status);
        const statusIcon = getOrderStatusIcon(status);
        const statusText = getOrderStatusText(status);

        return (
            <Card containerStyle={styles.orderCard}>
                <View style={[styles.orderHeader, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                    <View style={styles.orderInfo}>
                        <Text style={styles.orderTitle}>Orden #{index + 1}</Text>
                        <Text style={styles.customerName}>{item.customerName}</Text>
                        <Text style={styles.customerPhone}>{item.customerPhone}</Text>
                    </View>
                    
                    <View style={styles.statusContainer}>
                        <Icon
                            name={statusIcon}
                            type="material"
                            color={statusColor}
                            size={24}
                        />
                        <Text style={[styles.statusText, { color: statusColor }]}>
                            {statusText}
                        </Text>
                    </View>
                </View>

                <View style={styles.addressContainer}>
                    <Icon name="location-on" type="material" size={16} color={colors.GREY} />
                    <Text style={styles.addressText} numberOfLines={2}>
                        {item.deliveryAddress?.address}
                    </Text>
                </View>

                {item.products && item.products.length > 0 && (
                    <View style={styles.productsInfo}>
                        <Icon name="inventory" type="material" size={16} color={colors.BLUE} />
                        <Text style={styles.productsText}>
                            {item.products.length} producto(s)
                        </Text>
                    </View>
                )}

                <View style={styles.orderActions}>
                    <TouchableOpacity 
                        style={styles.actionButton}
                        onPress={() => openNavigation(item.deliveryAddress?.address)}
                    >
                        <Icon name="navigation" type="material" size={20} color={colors.WHITE} />
                        <Text style={styles.actionButtonText}>Navegar</Text>
                    </TouchableOpacity>

                    {status === 'pending' && (
                        <TouchableOpacity 
                            style={[styles.actionButton, { backgroundColor: colors.BLUE }]}
                            onPress={() => startOrder(index)}
                        >
                            <Icon name="play-arrow" type="material" size={20} color={colors.WHITE} />
                            <Text style={styles.actionButtonText}>Iniciar</Text>
                        </TouchableOpacity>
                    )}

                    {(status === 'in_progress' || status === 'partial') && (
                        <TouchableOpacity 
                            style={[styles.actionButton, { backgroundColor: colors.GREEN }]}
                            onPress={() => completeOrder(index)}
                        >
                            <Icon name="check" type="material" size={20} color={colors.WHITE} />
                            <Text style={styles.actionButtonText}>Completar</Text>
                        </TouchableOpacity>
                    )}
                </View>
            </Card>
        );
    };

    return (
        <View style={styles.container}>
            {/* Header con información del folio */}
            <View style={styles.header}>
                <Text style={styles.folioText}>Folio: {folio}</Text>
                <Text style={styles.progressText}>
                    {getCompletedOrdersCount()} / {orders.length} entregas completadas
                </Text>
                <Text style={styles.productsText}>
                    Total: {getTotalProductsCount()} productos
                </Text>
            </View>

            {/* Lista de órdenes */}
            <FlatList
                data={orders}
                renderItem={renderOrderCard}
                keyExtractor={(item, index) => `order-${index}`}
                showsVerticalScrollIndicator={false}
                contentContainerStyle={styles.ordersList}
            />




        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.BACKGROUND,
    },
    header: {
        backgroundColor: colors.WHITE,
        padding: 20,
        elevation: 2,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    folioText: {
        fontSize: 18,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginBottom: 5,
    },
    progressText: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.BLUE,
        marginBottom: 2,
    },
    productsText: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GREY,
    },
    ordersList: {
        padding: 15,
    },
    orderCard: {
        borderRadius: 10,
        marginBottom: 15,
        elevation: 3,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
    },
    orderHeader: {
        alignItems: 'center',
        marginBottom: 15,
    },
    orderInfo: {
        flex: 1,
    },
    orderTitle: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginBottom: 3,
    },
    customerName: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.BLACK,
        marginBottom: 2,
    },
    customerPhone: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GREY,
    },
    statusContainer: {
        alignItems: 'center',
    },
    statusText: {
        fontSize: 12,
        fontFamily: fonts.Bold,
        marginTop: 2,
    },
    addressContainer: {
        flexDirection: 'row',
        alignItems: 'flex-start',
        marginBottom: 10,
    },
    addressText: {
        flex: 1,
        fontSize: 13,
        fontFamily: fonts.Regular,
        color: colors.BLACK,
        marginLeft: 8,
        lineHeight: 18,
    },
    productsInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 15,
    },
    orderActions: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginTop: 10,
    },
    actionButton: {
        backgroundColor: colors.ORANGE,
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 15,
        paddingVertical: 10,
        borderRadius: 8,
        flex: 1,
        marginHorizontal: 5,
    },
    actionButtonText: {
        color: colors.WHITE,
        fontSize: 14,
        fontFamily: fonts.Bold,
        marginLeft: 5,
    },
});
