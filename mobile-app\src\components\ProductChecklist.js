import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    Modal,
    Alert
} from 'react-native';
import { Icon, <PERSON>ton, Card, CheckBox } from 'react-native-elements';
import { colors } from '../common/theme';
import { fonts } from '../common/font';
import i18n from 'i18n-js';

export default function ProductChecklist({ 
    visible, 
    onClose, 
    order, 
    onUpdateProducts,
    deliveryId 
}) {
    const { t } = i18n;
    const isRTL = i18n.locale.indexOf('he') === 0 || i18n.locale.indexOf('ar') === 0;
    
    const [checkedProducts, setCheckedProducts] = useState({});
    const [allDelivered, setAllDelivered] = useState(false);

    // Inicializar estado de productos cuando se abre el modal
    useEffect(() => {
        if (visible && order?.products) {
            const initialState = {};
            order.products.forEach(product => {
                initialState[product.id] = product.delivered || false;
            });
            setCheckedProducts(initialState);
            
            // Verificar si todos los productos están entregados
            const allDeliveredCheck = order.products.every(product => 
                initialState[product.id] === true
            );
            setAllDelivered(allDeliveredCheck);
        }
    }, [visible, order]);

    // Manejar cambio en checkbox de producto individual
    const handleProductCheck = (productId, isChecked) => {
        const newCheckedProducts = {
            ...checkedProducts,
            [productId]: isChecked
        };
        setCheckedProducts(newCheckedProducts);

        // Verificar si todos los productos están marcados
        const allChecked = order.products.every(product => 
            newCheckedProducts[product.id] === true
        );
        setAllDelivered(allChecked);
    };

    // Marcar todos los productos como entregados/no entregados
    const handleSelectAll = (selectAll) => {
        const newCheckedProducts = {};
        order.products.forEach(product => {
            newCheckedProducts[product.id] = selectAll;
        });
        setCheckedProducts(newCheckedProducts);
        setAllDelivered(selectAll);
    };

    // Guardar cambios y cerrar modal
    const handleSaveAndClose = () => {
        // Actualizar productos con estado de entrega
        const updatedProducts = order.products.map(product => ({
            ...product,
            delivered: checkedProducts[product.id] || false
        }));

        // Llamar función para actualizar en el componente padre
        onUpdateProducts(order.customerId, updatedProducts);
        
        Alert.alert(
            'Productos Actualizados',
            `Se han marcado ${Object.values(checkedProducts).filter(Boolean).length} de ${order.products.length} productos como entregados.`,
            [{ text: 'OK', onPress: onClose }]
        );
    };

    // Calcular estadísticas
    const deliveredCount = Object.values(checkedProducts).filter(Boolean).length;
    const totalCount = order?.products?.length || 0;
    const progressPercentage = totalCount > 0 ? (deliveredCount / totalCount) * 100 : 0;

    if (!order) return null;

    return (
        <Modal
            visible={visible}
            animationType="slide"
            presentationStyle="pageSheet"
            onRequestClose={onClose}
        >
            <View style={styles.container}>
                {/* Header */}
                <View style={styles.header}>
                    <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                        <Icon name="close" type="material" size={24} color={colors.BLACK} />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>Checklist de Productos</Text>
                    <View style={styles.placeholder} />
                </View>

                {/* Información del cliente */}
                <Card containerStyle={styles.customerCard}>
                    <View style={styles.customerInfo}>
                        <Icon name="person" type="material" size={24} color={colors.BLUE} />
                        <View style={styles.customerDetails}>
                            <Text style={styles.customerName}>{order.customerName}</Text>
                            <Text style={styles.customerAddress}>{order.deliveryAddress.address}</Text>
                        </View>
                    </View>
                    
                    {/* Progreso de entrega */}
                    <View style={styles.progressContainer}>
                        <Text style={styles.progressText}>
                            Progreso: {deliveredCount}/{totalCount} productos entregados
                        </Text>
                        <View style={styles.progressBar}>
                            <View 
                                style={[
                                    styles.progressFill, 
                                    { width: `${progressPercentage}%` }
                                ]} 
                            />
                        </View>
                        <Text style={styles.progressPercentage}>{Math.round(progressPercentage)}%</Text>
                    </View>
                </Card>

                {/* Controles de selección */}
                <View style={styles.controlsContainer}>
                    <TouchableOpacity 
                        style={styles.selectAllButton}
                        onPress={() => handleSelectAll(!allDelivered)}
                    >
                        <Icon 
                            name={allDelivered ? "check-box" : "check-box-outline-blank"} 
                            type="material" 
                            size={24} 
                            color={colors.BLUE} 
                        />
                        <Text style={styles.selectAllText}>
                            {allDelivered ? 'Desmarcar Todos' : 'Marcar Todos'}
                        </Text>
                    </TouchableOpacity>
                </View>

                {/* Lista de productos */}
                <ScrollView style={styles.productsList}>
                    {order.products.map((product, index) => (
                        <Card key={product.id} containerStyle={styles.productCard}>
                            <View style={styles.productRow}>
                                <CheckBox
                                    checked={checkedProducts[product.id] || false}
                                    onPress={() => handleProductCheck(product.id, !checkedProducts[product.id])}
                                    containerStyle={styles.checkboxContainer}
                                    checkedColor={colors.GREEN}
                                />
                                
                                <View style={styles.productInfo}>
                                    <Text style={styles.productName}>{product.name}</Text>
                                    <Text style={styles.productDetails}>
                                        Cantidad: {product.quantity} | Precio: ${product.price}
                                    </Text>
                                    {product.description && (
                                        <Text style={styles.productDescription}>{product.description}</Text>
                                    )}
                                </View>

                                <View style={styles.statusIndicator}>
                                    <Icon 
                                        name={checkedProducts[product.id] ? "check-circle" : "radio-button-unchecked"} 
                                        type="material" 
                                        size={20} 
                                        color={checkedProducts[product.id] ? colors.GREEN : colors.GREY} 
                                    />
                                </View>
                            </View>
                        </Card>
                    ))}
                </ScrollView>

                {/* Botones de acción */}
                <View style={styles.actionButtons}>
                    {/* Botón Cancelar */}
                    <TouchableOpacity
                        style={styles.cancelButton}
                        onPress={onClose}
                        activeOpacity={0.7}
                    >
                        <Text style={styles.cancelButtonText}>CANCELAR</Text>
                    </TouchableOpacity>

                    {/* Botón Guardar */}
                    <TouchableOpacity
                        style={[
                            styles.saveButton,
                            !allDelivered && styles.disabledButton
                        ]}
                        onPress={allDelivered ? handleSaveAndClose : null}
                        disabled={!allDelivered}
                        activeOpacity={allDelivered ? 0.7 : 1}
                    >
                        <Text style={[
                            styles.saveButtonText,
                            !allDelivered && styles.disabledButtonText
                        ]}>
                            GUARDAR CAMBIOS
                        </Text>
                    </TouchableOpacity>
                </View>
            </View>
        </Modal>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.WHITE,
    },
    header: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingHorizontal: 16,
        paddingVertical: 12,
        borderBottomWidth: 1,
        borderBottomColor: colors.BORDER_BACKGROUND,
        backgroundColor: colors.WHITE,
    },
    closeButton: {
        padding: 8,
    },
    headerTitle: {
        fontSize: 18,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
    },
    placeholder: {
        width: 40,
    },
    customerCard: {
        margin: 16,
        borderRadius: 8,
    },
    customerInfo: {
        flexDirection: 'row',
        alignItems: 'center',
        marginBottom: 12,
    },
    customerDetails: {
        marginLeft: 12,
        flex: 1,
    },
    customerName: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
    },
    customerAddress: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        marginTop: 2,
    },
    progressContainer: {
        marginTop: 8,
    },
    progressText: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.BLACK,
        marginBottom: 8,
    },
    progressBar: {
        height: 8,
        backgroundColor: colors.BORDER_BACKGROUND,
        borderRadius: 4,
        overflow: 'hidden',
    },
    progressFill: {
        height: '100%',
        backgroundColor: colors.GREEN,
    },
    progressPercentage: {
        fontSize: 12,
        fontFamily: fonts.Bold,
        color: colors.GREEN,
        textAlign: 'right',
        marginTop: 4,
    },
    controlsContainer: {
        paddingHorizontal: 16,
        marginBottom: 8,
    },
    selectAllButton: {
        flexDirection: 'row',
        alignItems: 'center',
        paddingVertical: 8,
    },
    selectAllText: {
        fontSize: 16,
        fontFamily: fonts.Regular,
        color: colors.BLUE,
        marginLeft: 8,
    },
    productsList: {
        flex: 1,
        paddingHorizontal: 16,
    },
    productCard: {
        marginBottom: 8,
        borderRadius: 8,
    },
    productRow: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    checkboxContainer: {
        margin: 0,
        padding: 0,
    },
    productInfo: {
        flex: 1,
        marginLeft: 8,
    },
    productName: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
    },
    productDetails: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        marginTop: 2,
    },
    productDescription: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        marginTop: 4,
        fontStyle: 'italic',
    },
    statusIndicator: {
        marginLeft: 8,
    },
    actionButtons: {
        flexDirection: 'row',
        paddingHorizontal: 16,
        paddingVertical: 16,
        borderTopWidth: 1,
        borderTopColor: colors.BORDER_BACKGROUND,
        backgroundColor: colors.WHITE,
    },
    cancelButton: {
        flex: 1,
        marginRight: 8,
        backgroundColor: '#FFFFFF',
        borderWidth: 2,
        borderColor: '#C0C0C0',
        borderRadius: 8,
        paddingVertical: 16,
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 50,
    },
    cancelButtonText: {
        color: '#000000',
        fontSize: 16,
        fontWeight: '700',
        textAlign: 'center',
        letterSpacing: 0.5,
    },
    saveButton: {
        flex: 1,
        marginLeft: 8,
        backgroundColor: '#0cab03',
        borderRadius: 8,
        paddingVertical: 16,
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: 50,
    },
    saveButtonText: {
        color: '#FFFFFF',
        fontSize: 16,
        fontWeight: '700',
        textAlign: 'center',
        letterSpacing: 0.5,
    },
    disabledButton: {
        backgroundColor: '#C0C0C0',
        opacity: 0.7,
    },
    disabledButtonText: {
        color: '#FFFFFF',
        opacity: 0.9,
    },
});
