import React, { useState, useEffect } from 'react';
import {
    View,
    Text,
    StyleSheet,
    ScrollView,
    TouchableOpacity,
    Alert,
    RefreshControl
} from 'react-native';
import { Icon, <PERSON><PERSON>, Card } from 'react-native-elements';
import { colors } from '../common/theme';
import { fonts } from '../common/font';
import i18n from 'i18n-js';
import { useSelector, useDispatch } from 'react-redux';
import { api } from 'common';
import { GroupedDeliveryDetails } from '../components';
import ProductChecklist from '../components/ProductChecklist';

export default function GroupedDeliveryScreen(props) {
    const { t } = i18n;
    const isRTL = i18n.locale.indexOf('he') === 0 || i18n.locale.indexOf('ar') === 0;
    
    const dispatch = useDispatch();
    const auth = useSelector(state => state.auth);
    const [groupedDeliveries, setGroupedDeliveries] = useState([]);
    const [selectedDelivery, setSelectedDelivery] = useState(null);
    const [refreshing, setRefreshing] = useState(false);
    const [loading, setLoading] = useState(true);

    // Estados para el checklist de productos
    const [checklistVisible, setChecklistVisible] = useState(false);
    const [selectedOrder, setSelectedOrder] = useState(null);
    const [currentDeliveryId, setCurrentDeliveryId] = useState(null);

    const { updateBooking } = api;

    useEffect(() => {
        loadGroupedDeliveries();
    }, []);

    const loadGroupedDeliveries = async () => {
        try {
            setLoading(true);
            // Aquí cargarías las entregas agrupadas desde Firebase
            // Por ahora usamos datos de ejemplo
            const mockDeliveries = [
                {
                    id: 'GD001',
                    folio: 'ENT-2024-001',
                    driver: auth.profile?.uid,
                    status: 'assigned',
                    createdAt: new Date().getTime(),
                    orders: [
                        {
                            customerId: 'customer1',
                            bookingId: 'booking_customer1_001', // ← AGREGADO PARA TESTING
                            customerName: 'María García',
                            customerPhone: '+52 33 1234 5678',
                            deliveryAddress: {
                                lat: 20.6597,
                                lng: -103.3496,
                                address: 'Av. Vallarta 1234, Guadalajara'
                            },
                            products: [
                                {
                                    id: 'PROD001',
                                    name: 'Obra de Arte "Paisaje Urbano"',
                                    quantity: 1,
                                    price: 2500.00,
                                    sku: 'ART-001',
                                    description: 'Pintura al óleo sobre lienzo, 60x80 cm',
                                    fragile: true,
                                    delivered: false,
                                    notes: 'Manejar con extremo cuidado'
                                },
                                {
                                    id: 'PROD004',
                                    name: 'Marco Premium Dorado',
                                    quantity: 1,
                                    price: 450.00,
                                    sku: 'FRAME-004',
                                    description: 'Marco de madera con acabado dorado',
                                    fragile: true,
                                    delivered: false
                                }
                            ],
                            status: 'pending'
                        },
                        {
                            customerId: 'customer2',
                            bookingId: 'booking_customer2_002', // ← AGREGADO PARA TESTING
                            customerName: 'Juan Pérez',
                            customerPhone: '+52 33 8765 4321',
                            deliveryAddress: {
                                lat: 20.6736,
                                lng: -103.3370,
                                address: 'Calle Morelos 567, Guadalajara'
                            },
                            products: [
                                {
                                    id: 'PROD002',
                                    name: 'Marco Decorativo Clásico',
                                    quantity: 2,
                                    price: 180.00,
                                    sku: 'FRAME-002',
                                    description: 'Marco de madera color nogal, 40x50 cm',
                                    fragile: true,
                                    delivered: false
                                },
                                {
                                    id: 'PROD003',
                                    name: 'Catálogo de Arte Contemporáneo',
                                    quantity: 5,
                                    price: 75.00,
                                    sku: 'CAT-003',
                                    description: 'Catálogo impreso en papel couché',
                                    fragile: false,
                                    delivered: false
                                },
                                {
                                    id: 'PROD005',
                                    name: 'Set de Pinceles Profesionales',
                                    quantity: 1,
                                    price: 320.00,
                                    sku: 'BRUSH-005',
                                    description: 'Set de 12 pinceles de diferentes tamaños',
                                    fragile: false,
                                    delivered: false
                                }
                            ],
                            status: 'pending'
                        }
                    ]
                }
            ];
            setGroupedDeliveries(mockDeliveries);
        } catch (error) {
            Alert.alert('Error', 'No se pudieron cargar las entregas agrupadas');
        } finally {
            setLoading(false);
            setRefreshing(false);
        }
    };

    const onRefresh = () => {
        setRefreshing(true);
        loadGroupedDeliveries();
    };

    const acceptGroupedDelivery = (delivery) => {
        Alert.alert(
            'Aceptar Entrega Agrupada',
            `¿Deseas aceptar la entrega con folio ${delivery.folio}?`,
            [
                { text: 'Cancelar', style: 'cancel' },
                { 
                    text: 'Aceptar', 
                    onPress: () => {
                        // Actualizar estado de la entrega
                        setGroupedDeliveries(prev => 
                            prev.map(d => 
                                d.id === delivery.id 
                                    ? { ...d, status: 'accepted' }
                                    : d
                            )
                        );
                        Alert.alert('Éxito', 'Entrega agrupada aceptada');
                    }
                }
            ]
        );
    };

    const startGroupedDelivery = (delivery) => {
        setSelectedDelivery(delivery);
    };

    // Funciones para manejar el checklist de productos
    const openProductChecklist = (order, deliveryId) => {
        setSelectedOrder(order);
        setCurrentDeliveryId(deliveryId);
        setChecklistVisible(true);
    };

    const closeProductChecklist = () => {
        setChecklistVisible(false);
        setSelectedOrder(null);
        setCurrentDeliveryId(null);
    };

    const updateOrderProducts = async (customerId, updatedProducts) => {
        // 1. Actualizar estado local (interfaz)
        setGroupedDeliveries(prevDeliveries => {
            return prevDeliveries.map(delivery => {
                if (delivery.id === currentDeliveryId) {
                    const updatedOrders = delivery.orders.map(order => {
                        if (order.customerId === customerId) {
                            return {
                                ...order,
                                products: updatedProducts
                            };
                        }
                        return order;
                    });
                    return { ...delivery, orders: updatedOrders };
                }
                return delivery;
            });
        });

        // 2. NUEVO: Guardar en Firebase/Base de datos
        try {
            // Encontrar el booking correspondiente
            const currentDelivery = groupedDeliveries.find(d => d.id === currentDeliveryId);
            const currentOrder = currentDelivery?.orders.find(o => o.customerId === customerId);

            if (currentOrder && currentOrder.bookingId) {
                // Crear objeto de booking actualizado
                const updatedBooking = {
                    id: currentOrder.bookingId,
                    products: updatedProducts,
                    productChecklistUpdated: true,
                    productChecklistTimestamp: new Date().getTime(),
                    groupedDeliveryId: currentDeliveryId,
                    customerId: customerId
                };

                console.log('🔄 Guardando productos en Firebase:', updatedBooking);

                // Guardar en Firebase usando updateBooking
                dispatch(updateBooking(updatedBooking));

                console.log('✅ Productos guardados exitosamente en la base de datos');
            } else {
                console.warn('⚠️ No se encontró bookingId para guardar los productos');
            }
        } catch (error) {
            console.error('❌ Error al guardar productos en base de datos:', error);
            Alert.alert(
                'Error de Conexión',
                'No se pudieron guardar los cambios en la base de datos. Los cambios se mantienen localmente.',
                [{ text: 'OK' }]
            );
        }
    };

    const handleCompleteOrder = (orderIndex, deliveryData) => {
        // Actualizar el estado de la orden específica
        setSelectedDelivery(prev => {
            const updatedOrders = [...prev.orders];
            updatedOrders[orderIndex] = {
                ...updatedOrders[orderIndex],
                status: deliveryData.verification.partialDelivery ? 'partial' : 'completed',
                deliveryVerification: deliveryData
            };
            return { ...prev, orders: updatedOrders };
        });

        // Verificar si todas las órdenes están completadas
        const allCompleted = selectedDelivery.orders.every((order, index) => {
            if (index === orderIndex) {
                return true; // La orden actual se acaba de completar
            }
            return order.status === 'completed' || order.status === 'partial';
        });

        if (allCompleted) {
            Alert.alert(
                'Entrega Agrupada Completada',
                'Todas las entregas han sido completadas exitosamente',
                [
                    {
                        text: 'OK',
                        onPress: () => {
                            setSelectedDelivery(null);
                            loadGroupedDeliveries(); // Recargar la lista
                        }
                    }
                ]
            );
        }
    };

    const getDeliveryStatusColor = (status) => {
        switch (status) {
            case 'completed': return colors.GREEN;
            case 'accepted': return colors.BLUE;
            case 'assigned': return colors.ORANGE;
            default: return colors.GREY;
        }
    };

    const getDeliveryStatusText = (status) => {
        switch (status) {
            case 'completed': return 'Completada';
            case 'accepted': return 'Aceptada';
            case 'assigned': return 'Asignada';
            default: return 'Pendiente';
        }
    };

    const renderDeliveryCard = (delivery) => {
        const statusColor = getDeliveryStatusColor(delivery.status);
        const statusText = getDeliveryStatusText(delivery.status);
        const totalProducts = delivery.orders.reduce((sum, order) => sum + (order.products?.length || 0), 0);

        return (
            <Card key={delivery.id} containerStyle={styles.deliveryCard}>
                <View style={[styles.cardHeader, { flexDirection: isRTL ? 'row-reverse' : 'row' }]}>
                    <View style={styles.deliveryInfo}>
                        <Text style={styles.folioText}>Folio: {delivery.folio}</Text>
                        <Text style={styles.ordersCount}>{delivery.orders.length} entregas</Text>
                        <Text style={styles.productsCount}>{totalProducts} productos</Text>
                    </View>
                    
                    <View style={styles.statusContainer}>
                        <View style={[styles.statusBadge, { backgroundColor: statusColor }]}>
                            <Text style={styles.statusText}>{statusText}</Text>
                        </View>
                    </View>
                </View>

                <View style={styles.ordersPreview}>
                    {delivery.orders.map((order, index) => {
                        const deliveredProducts = order.products?.filter(p => p.delivered).length || 0;
                        const totalProducts = order.products?.length || 0;
                        const progressPercentage = totalProducts > 0 ? (deliveredProducts / totalProducts) * 100 : 0;

                        return (
                            <View key={index} style={styles.orderRow}>
                                <View style={styles.orderInfo}>
                                    <Text style={styles.orderPreview} numberOfLines={1}>
                                        • {order.customerName} - {order.deliveryAddress.address}
                                    </Text>
                                    <Text style={styles.productProgress}>
                                        {deliveredProducts}/{totalProducts} productos entregados ({Math.round(progressPercentage)}%)
                                    </Text>
                                </View>

                                {(delivery.status === 'accepted' || delivery.status === 'in_progress') && (
                                    <TouchableOpacity
                                        style={styles.checklistButton}
                                        onPress={() => openProductChecklist(order, delivery.id)}
                                    >
                                        <Icon
                                            name="checklist"
                                            type="material"
                                            size={20}
                                            color={colors.BLUE}
                                        />
                                        <Text style={styles.checklistButtonText}>Checklist</Text>
                                    </TouchableOpacity>
                                )}
                            </View>
                        );
                    })}
                </View>

                <View style={styles.cardActions}>
                    {delivery.status === 'assigned' && (
                        <Button
                            title="Aceptar"
                            onPress={() => acceptGroupedDelivery(delivery)}
                            buttonStyle={[styles.actionButton, { backgroundColor: colors.GREEN }]}
                            titleStyle={styles.actionButtonText}
                        />
                    )}
                    
                    {delivery.status === 'accepted' && (
                        <Button
                            title="Iniciar Entregas"
                            onPress={() => startGroupedDelivery(delivery)}
                            buttonStyle={[styles.actionButton, { backgroundColor: colors.BLUE }]}
                            titleStyle={styles.actionButtonText}
                        />
                    )}
                </View>
            </Card>
        );
    };

    if (selectedDelivery) {
        return (
            <View style={styles.container}>
                <View style={styles.header}>
                    <TouchableOpacity 
                        onPress={() => setSelectedDelivery(null)}
                        style={styles.backButton}
                    >
                        <Icon
                            name={isRTL ? "arrow-forward" : "arrow-back"}
                            type="material"
                            color={colors.WHITE}
                            size={24}
                        />
                    </TouchableOpacity>
                    <Text style={styles.headerTitle}>Entrega Agrupada</Text>
                </View>
                
                <GroupedDeliveryDetails
                    deliveryData={selectedDelivery}
                    onCompleteOrder={handleCompleteOrder}
                />
            </View>
        );
    }

    return (
        <View style={styles.container}>
            <View style={styles.header}>
                <TouchableOpacity 
                    onPress={() => props.navigation.goBack()}
                    style={styles.backButton}
                >
                    <Icon
                        name={isRTL ? "arrow-forward" : "arrow-back"}
                        type="material"
                        color={colors.WHITE}
                        size={24}
                    />
                </TouchableOpacity>
                <Text style={styles.headerTitle}>Entregas Agrupadas</Text>
            </View>

            <ScrollView
                style={styles.content}
                refreshControl={
                    <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
                }
            >
                {loading ? (
                    <View style={styles.loadingContainer}>
                        <Text style={styles.loadingText}>Cargando entregas...</Text>
                    </View>
                ) : groupedDeliveries.length === 0 ? (
                    <View style={styles.emptyContainer}>
                        <Icon name="local-shipping" type="material" size={64} color={colors.GREY} />
                        <Text style={styles.emptyText}>No hay entregas agrupadas disponibles</Text>
                    </View>
                ) : (
                    groupedDeliveries.map(delivery => renderDeliveryCard(delivery))
                )}
            </ScrollView>

            {/* Modal de checklist de productos */}
            <ProductChecklist
                visible={checklistVisible}
                onClose={closeProductChecklist}
                order={selectedOrder}
                onUpdateProducts={updateOrderProducts}
                deliveryId={currentDeliveryId}
            />
        </View>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: colors.BACKGROUND,
    },
    header: {
        backgroundColor: colors.HEADER,
        paddingTop: 50,
        paddingBottom: 15,
        paddingHorizontal: 20,
        flexDirection: 'row',
        alignItems: 'center',
        elevation: 3,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 2,
    },
    backButton: {
        marginRight: 15,
        padding: 5,
    },
    headerTitle: {
        fontSize: 20,
        fontFamily: fonts.Bold,
        color: colors.WHITE,
        flex: 1,
    },
    content: {
        flex: 1,
        padding: 15,
    },
    deliveryCard: {
        borderRadius: 10,
        marginBottom: 15,
        elevation: 3,
        shadowColor: colors.BLACK,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 3,
    },
    cardHeader: {
        alignItems: 'center',
        marginBottom: 15,
    },
    deliveryInfo: {
        flex: 1,
    },
    folioText: {
        fontSize: 16,
        fontFamily: fonts.Bold,
        color: colors.BLACK,
        marginBottom: 3,
    },
    ordersCount: {
        fontSize: 14,
        fontFamily: fonts.Regular,
        color: colors.BLUE,
        marginBottom: 2,
    },
    productsCount: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.GREY,
    },
    statusContainer: {
        alignItems: 'flex-end',
    },
    statusBadge: {
        paddingHorizontal: 12,
        paddingVertical: 6,
        borderRadius: 15,
    },
    statusText: {
        fontSize: 12,
        fontFamily: fonts.Bold,
        color: colors.WHITE,
    },
    ordersPreview: {
        marginBottom: 15,
    },
    orderPreview: {
        fontSize: 13,
        fontFamily: fonts.Regular,
        color: colors.BLACK,
        marginBottom: 3,
    },
    moreOrders: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.BLUE,
        fontStyle: 'italic',
    },
    cardActions: {
        marginTop: 10,
    },
    actionButton: {
        borderRadius: 8,
        paddingVertical: 12,
    },
    actionButtonText: {
        fontSize: 14,
        fontFamily: fonts.Bold,
        color: colors.WHITE,
    },
    loadingContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingTop: 50,
    },
    loadingText: {
        fontSize: 16,
        fontFamily: fonts.Regular,
        color: colors.GREY,
    },
    emptyContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        paddingTop: 100,
    },
    emptyText: {
        fontSize: 16,
        fontFamily: fonts.Regular,
        color: colors.GREY,
        textAlign: 'center',
        marginTop: 20,
    },
    orderRow: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginBottom: 8,
        paddingVertical: 4,
    },
    orderInfo: {
        flex: 1,
        marginRight: 8,
    },
    productProgress: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.BLUE,
        marginTop: 2,
        marginLeft: 10,
    },
    checklistButton: {
        flexDirection: 'row',
        alignItems: 'center',
        backgroundColor: colors.WHITE,
        borderWidth: 1,
        borderColor: colors.BLUE,
        borderRadius: 6,
        paddingHorizontal: 8,
        paddingVertical: 4,
    },
    checklistButtonText: {
        fontSize: 12,
        fontFamily: fonts.Regular,
        color: colors.BLUE,
        marginLeft: 4,
    },
});
